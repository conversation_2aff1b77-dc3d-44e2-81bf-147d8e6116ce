using System.ServiceModel;
using MaterialsWebService.Models;

namespace MaterialsWebService.Services
{
    /// <summary>
    /// 辅料高架库库存接口
    /// </summary>
    [ServiceContract(Namespace = "http://bywms.bj.com/MaterialsInventory")]
    public interface IMaterialsInventoryService
    {
        /// <summary>
        /// 获取辅料库存信息
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <returns>库存信息</returns>
        [OperationContract]
        Message<MaterialsInventoryItem> GetMaterialsInventoryBill(string date);

        /// <summary>
        /// 按品牌查询辅料库存
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <param name="brandCode">品牌编码</param>
        /// <returns>库存信息</returns>
        [OperationContract]
        Message<MaterialsInventoryItem> GetMaterialsInventoryByBrand(string date, string brandCode);

        /// <summary>
        /// 按物料编码查询库存
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <param name="materialCode">物料编码</param>
        /// <returns>库存信息</returns>
        [OperationContract]
        Message<MaterialsInventoryItem> GetMaterialsInventoryByMaterial(string date, string materialCode);

        /// <summary>
        /// 实时库存查询
        /// </summary>
        /// <returns>实时库存信息</returns>
        [OperationContract]
        Message<MaterialsInventoryItem> GetRealTimeInventory();
    }
}
