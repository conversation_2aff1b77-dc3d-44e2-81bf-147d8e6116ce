using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using MaterialsWebService.Models;

namespace MaterialsWebService.Services
{
    /// <summary>
    /// 辅料高架库库存服务实现
    /// </summary>
    [ServiceBehavior(Namespace = "http://bywms.bj.com/MaterialsInventory")]
    public class MaterialsInventoryService : IMaterialsInventoryService
    {
        /// <summary>
        /// 获取辅料库存信息
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <returns>库存信息</returns>
        public Message<MaterialsInventoryItem> GetMaterialsInventoryBill(string date)
        {
            try
            {
                if (string.IsNullOrEmpty(date))
                {
                    date = DateTime.Now.ToString("yyyyMMdd");
                }

                // 模拟获取库存数据
                var inventoryItems = GenerateSampleInventoryData(date);

                return CreateSuccessResponse(inventoryItems);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"获取库存信息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 按品牌查询辅料库存
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <param name="brandCode">品牌编码</param>
        /// <returns>库存信息</returns>
        public Message<MaterialsInventoryItem> GetMaterialsInventoryByBrand(string date, string brandCode)
        {
            try
            {
                if (string.IsNullOrEmpty(date))
                {
                    date = DateTime.Now.ToString("yyyyMMdd");
                }

                if (string.IsNullOrEmpty(brandCode))
                {
                    return CreateErrorResponse("品牌编码不能为空");
                }

                // 模拟按品牌查询库存数据
                var inventoryItems = GenerateSampleInventoryData(date)
                    .Where(item => item.BrandCode == brandCode)
                    .ToList();

                return CreateSuccessResponse(inventoryItems);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"按品牌查询库存时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 按物料编码查询库存
        /// </summary>
        /// <param name="date">查询日期</param>
        /// <param name="materialCode">物料编码</param>
        /// <returns>库存信息</returns>
        public Message<MaterialsInventoryItem> GetMaterialsInventoryByMaterial(string date, string materialCode)
        {
            try
            {
                if (string.IsNullOrEmpty(date))
                {
                    date = DateTime.Now.ToString("yyyyMMdd");
                }

                if (string.IsNullOrEmpty(materialCode))
                {
                    return CreateErrorResponse("物料编码不能为空");
                }

                // 模拟按物料编码查询库存数据
                var inventoryItems = GenerateSampleInventoryData(date)
                    .Where(item => item.MaterialCode == materialCode)
                    .ToList();

                return CreateSuccessResponse(inventoryItems);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"按物料编码查询库存时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 实时库存查询
        /// </summary>
        /// <returns>实时库存信息</returns>
        public Message<MaterialsInventoryItem> GetRealTimeInventory()
        {
            try
            {
                var currentDate = DateTime.Now.ToString("yyyyMMdd");
                var inventoryItems = GenerateSampleInventoryData(currentDate);

                return CreateSuccessResponse(inventoryItems);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"获取实时库存时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成示例库存数据
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>库存数据列表</returns>
        private List<MaterialsInventoryItem> GenerateSampleInventoryData(string date)
        {
            return new List<MaterialsInventoryItem>
            {
                new MaterialsInventoryItem
                {
                    Action = "QUERY",
                    Date = date,
                    BrandCode = "6901028058711",
                    BrandName = "好猫(长乐九美窄版)",
                    MaterialCode = "8616048FL",
                    MaterialName = "通风滤棒(蚌埠黄山)",
                    SupplierCode = "",
                    SupplierName = "",
                    MaterialUnitCode = "WZI",
                    MaterialUnitName = "万支",
                    Quantity = "192.40"
                },
                new MaterialsInventoryItem
                {
                    Action = "QUERY",
                    Date = date,
                    BrandCode = "6901028937429",
                    BrandName = "延安(细支圣地河谷)",
                    MaterialCode = "8222005FL",
                    MaterialName = "120mmBOPP透明抗红收缩膜(金田塑业)",
                    SupplierCode = "",
                    SupplierName = "",
                    MaterialUnitCode = "JUA",
                    MaterialUnitName = "卷",
                    Quantity = "5"
                },
                new MaterialsInventoryItem
                {
                    Action = "QUERY",
                    Date = date,
                    BrandCode = "6901028936668",
                    BrandName = "好猫(吉祥)",
                    MaterialCode = "6541261FL",
                    MaterialName = "60CU竖罗纹卷烟纸30g(浙江华丰纸业)",
                    SupplierCode = "",
                    SupplierName = "",
                    MaterialUnitCode = "PAN",
                    MaterialUnitName = "盘",
                    Quantity = "211"
                }
            };
        }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <returns>成功响应消息</returns>
        private Message<MaterialsInventoryItem> CreateSuccessResponse(List<MaterialsInventoryItem> data)
        {
            return new Message<MaterialsInventoryItem>
            {
                Head = CreateSuccessHead("调用成功"),
                Data = new MessageData<MaterialsInventoryItem>
                {
                    Table = new MessageTable<MaterialsInventoryItem>
                    {
                        TableName = "MTABLE",
                        Rows = data
                    }
                }
            };
        }

        /// <summary>
        /// 创建错误响应
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>错误响应消息</returns>
        private Message<MaterialsInventoryItem> CreateErrorResponse(string errorMessage)
        {
            return new Message<MaterialsInventoryItem>
            {
                Head = CreateErrorHead(errorMessage),
                Data = new MessageData<MaterialsInventoryItem>
                {
                    Table = new MessageTable<MaterialsInventoryItem>
                    {
                        TableName = "MTABLE",
                        Rows = new List<MaterialsInventoryItem>()
                    }
                }
            };
        }

        /// <summary>
        /// 创建成功消息头
        /// </summary>
        /// <param name="message">成功信息</param>
        /// <returns>成功消息头</returns>
        private MessageHead CreateSuccessHead(string message)
        {
            return new MessageHead
            {
                Id = "",
                Name = "辅料高架库库存",
                Source = "BYNMES",
                Target = "BYWMS",
                ServiceName = "getMaterialsInventoryBill",
                MessageType = "1",
                ReturnCode = "1",
                ReturnDescription = message,
                Date = DateTime.Now.ToString("yyyy/M/d H:m:s")
            };
        }

        /// <summary>
        /// 创建错误消息头
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>错误消息头</returns>
        private MessageHead CreateErrorHead(string errorMessage)
        {
            return new MessageHead
            {
                Id = "",
                Name = "辅料高架库库存",
                Source = "BYNMES",
                Target = "BYWMS",
                ServiceName = "getMaterialsInventoryBill",
                MessageType = "1",
                ReturnCode = "0",
                ReturnDescription = errorMessage,
                Date = DateTime.Now.ToString("yyyy/M/d H:m:s")
            };
        }
    }
}
