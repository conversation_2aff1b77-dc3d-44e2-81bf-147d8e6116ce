using System.ServiceModel;
using MaterialsWebService.Models;

namespace MaterialsWebService.Services
{
    /// <summary>
    /// 辅料基本属性接口
    /// </summary>
    [ServiceContract(Namespace = "http://bywms.bj.com/BaseDataAuxiliary")]
    public interface IBaseDataAuxiliaryService
    {
        /// <summary>
        /// 同步辅料基本属性数据
        /// </summary>
        /// <param name="message">辅料基本属性消息</param>
        /// <returns>同步结果</returns>
        [OperationContract]
        Message<BaseDataAuxiliaryItem> SyncBaseDataAuxiliary(Message<BaseDataAuxiliaryItem> message);

        /// <summary>
        /// 查询辅料基本属性
        /// </summary>
        /// <param name="materialCode">物料编码</param>
        /// <returns>辅料基本属性信息</returns>
        [OperationContract]
        Message<BaseDataAuxiliaryItem> QueryBaseDataAuxiliary(string materialCode);

        /// <summary>
        /// 批量查询辅料基本属性
        /// </summary>
        /// <param name="materialCodes">物料编码列表</param>
        /// <returns>辅料基本属性信息列表</returns>
        [OperationContract]
        Message<BaseDataAuxiliaryItem> BatchQueryBaseDataAuxiliary(string[] materialCodes);
    }
}
