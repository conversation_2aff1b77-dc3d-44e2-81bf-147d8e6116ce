using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using MaterialsWebService.Models;

namespace MaterialsWebService.Services
{
    /// <summary>
    /// 辅料基本属性服务实现
    /// </summary>
    [ServiceBehavior(Namespace = "http://bywms.bj.com/BaseDataAuxiliary")]
    public class BaseDataAuxiliaryService : IBaseDataAuxiliaryService
    {
        /// <summary>
        /// 同步辅料基本属性数据
        /// </summary>
        /// <param name="message">辅料基本属性消息</param>
        /// <returns>同步结果</returns>
        public Message<BaseDataAuxiliaryItem> SyncBaseDataAuxiliary(Message<BaseDataAuxiliaryItem> message)
        {
            try
            {
                // 验证消息头
                if (message?.Head == null)
                {
                    return CreateErrorResponse("消息头不能为空");
                }

                // 验证数据
                if (message.Data?.Table?.Rows == null || message.Data.Table.Rows.Count == 0)
                {
                    return CreateErrorResponse("辅料基本属性数据不能为空");
                }

                // 处理辅料基本属性数据
                var processedItems = new List<BaseDataAuxiliaryItem>();
                foreach (var item in message.Data.Table.Rows)
                {
                    var processedItem = ProcessBaseDataItem(item);
                    processedItems.Add(processedItem);
                }

                // 返回成功响应
                return CreateSuccessResponse(processedItems);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"同步辅料基本属性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 查询辅料基本属性
        /// </summary>
        /// <param name="materialCode">物料编码</param>
        /// <returns>辅料基本属性信息</returns>
        public Message<BaseDataAuxiliaryItem> QueryBaseDataAuxiliary(string materialCode)
        {
            try
            {
                if (string.IsNullOrEmpty(materialCode))
                {
                    return CreateErrorResponse("物料编码不能为空");
                }

                // 模拟查询辅料基本属性
                var item = new BaseDataAuxiliaryItem
                {
                    Action = "QUERY",
                    Index = "1",
                    SystemIdKey = "8734945",
                    SystemUserCode = materialCode,
                    SystemCodeName = "辅料基本属性",
                    MaterialTypeId = "40050",
                    UnitId = "WZH",
                    SystemUseMark = "1",
                    SystemLevelCode = "000540",
                    SystemOrderCode = "548",
                    EsbId = materialCode
                };

                return CreateSuccessResponse(new List<BaseDataAuxiliaryItem> { item });
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"查询辅料基本属性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量查询辅料基本属性
        /// </summary>
        /// <param name="materialCodes">物料编码列表</param>
        /// <returns>辅料基本属性信息列表</returns>
        public Message<BaseDataAuxiliaryItem> BatchQueryBaseDataAuxiliary(string[] materialCodes)
        {
            try
            {
                if (materialCodes == null || materialCodes.Length == 0)
                {
                    return CreateErrorResponse("物料编码列表不能为空");
                }

                var items = new List<BaseDataAuxiliaryItem>();
                for (int i = 0; i < materialCodes.Length; i++)
                {
                    var item = new BaseDataAuxiliaryItem
                    {
                        Action = "QUERY",
                        Index = (i + 1).ToString(),
                        SystemIdKey = (8734945 + i).ToString(),
                        SystemUserCode = materialCodes[i],
                        SystemCodeName = $"辅料基本属性_{i + 1}",
                        MaterialTypeId = "40050",
                        UnitId = "WZH",
                        SystemUseMark = "1",
                        SystemLevelCode = "000540",
                        SystemOrderCode = (548 + i).ToString(),
                        EsbId = materialCodes[i]
                    };
                    items.Add(item);
                }

                return CreateSuccessResponse(items);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"批量查询辅料基本属性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个辅料基本属性项
        /// </summary>
        /// <param name="item">辅料基本属性项</param>
        /// <returns>处理后的项</returns>
        private BaseDataAuxiliaryItem ProcessBaseDataItem(BaseDataAuxiliaryItem item)
        {
            // 验证必要字段
            if (string.IsNullOrEmpty(item.SystemUserCode))
            {
                throw new ArgumentException("系统用户编码不能为空");
            }

            // 这里应该实现实际的数据处理逻辑
            // 例如：保存到数据库、验证数据等

            return item;
        }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <returns>成功响应消息</returns>
        private Message<BaseDataAuxiliaryItem> CreateSuccessResponse(List<BaseDataAuxiliaryItem> data)
        {
            return new Message<BaseDataAuxiliaryItem>
            {
                Head = CreateSuccessHead("处理成功"),
                Data = new MessageData<BaseDataAuxiliaryItem>
                {
                    Table = new MessageTable<BaseDataAuxiliaryItem>
                    {
                        TableName = "5538200",
                        Rows = data
                    }
                }
            };
        }

        /// <summary>
        /// 创建错误响应
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>错误响应消息</returns>
        private Message<BaseDataAuxiliaryItem> CreateErrorResponse(string errorMessage)
        {
            return new Message<BaseDataAuxiliaryItem>
            {
                Head = CreateErrorHead(errorMessage),
                Data = new MessageData<BaseDataAuxiliaryItem>
                {
                    Table = new MessageTable<BaseDataAuxiliaryItem>
                    {
                        TableName = "5538200",
                        Rows = new List<BaseDataAuxiliaryItem>()
                    }
                }
            };
        }

        /// <summary>
        /// 创建成功消息头
        /// </summary>
        /// <param name="message">成功信息</param>
        /// <returns>成功消息头</returns>
        private MessageHead CreateSuccessHead(string message)
        {
            return new MessageHead
            {
                Id = Guid.NewGuid().ToString("N"),
                Name = "辅料基本属性",
                Source = "BYWMS",
                Target = "BYBAS",
                ServiceName = "BaseDataAuxiliary",
                MessageType = "1",
                ReturnCode = "1",
                ReturnDescription = message,
                Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
            };
        }

        /// <summary>
        /// 创建错误消息头
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>错误消息头</returns>
        private MessageHead CreateErrorHead(string errorMessage)
        {
            return new MessageHead
            {
                Id = Guid.NewGuid().ToString("N"),
                Name = "辅料基本属性",
                Source = "BYWMS",
                Target = "BYBAS",
                ServiceName = "BaseDataAuxiliary",
                MessageType = "1",
                ReturnCode = "0",
                ReturnDescription = errorMessage,
                Date = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
            };
        }
    }
}
