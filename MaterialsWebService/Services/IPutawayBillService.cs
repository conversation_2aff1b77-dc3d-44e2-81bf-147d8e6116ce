using System.ServiceModel;
using MaterialsWebService.Models;

namespace MaterialsWebService.Services
{
    /// <summary>
    /// 辅料高架库出库接口
    /// </summary>
    [ServiceContract(Namespace = "http://bywms.bj.com/PutawayBill_FL")]
    public interface IPutawayBillService
    {
        /// <summary>
        /// 处理辅料出库单据
        /// </summary>
        /// <param name="message">出库单据消息</param>
        /// <returns>处理结果</returns>
        [OperationContract]
        Message<PutawayBillHeader> ProcessPutawayBill(Message<PutawayBillHeader> message);

        /// <summary>
        /// 查询出库单据状态
        /// </summary>
        /// <param name="documentNumber">单据编号</param>
        /// <returns>单据状态信息</returns>
        [OperationContract]
        Message<PutawayBillHeader> QueryPutawayBillStatus(string documentNumber);

        /// <summary>
        /// 取消出库单据
        /// </summary>
        /// <param name="documentNumber">单据编号</param>
        /// <param name="reason">取消原因</param>
        /// <returns>取消结果</returns>
        [OperationContract]
        MessageHead CancelPutawayBill(string documentNumber, string reason);
    }
}
