<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://bywms.bj.com/BaseDataAuxiliary"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             targetNamespace="http://bywms.bj.com/BaseDataAuxiliary"
             elementFormDefault="qualified">

  <!-- 类型定义 -->
  <types>
    <xsd:schema targetNamespace="http://bywms.bj.com/BaseDataAuxiliary" elementFormDefault="qualified">
      
      <!-- 消息头类型 -->
      <xsd:complexType name="MessageHead">
        <xsd:sequence>
          <xsd:element name="Id" type="xsd:string" minOccurs="0"/>
          <xsd:element name="n" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Source" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Target" type="xsd:string" minOccurs="0"/>
          <xsd:element name="SerName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="MsgType" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtCode" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtDesc" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup1" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup2" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup3" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup4" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup5" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Date" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Attrs" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 辅料基本属性项类型 -->
      <xsd:complexType name="BaseDataAuxiliaryItem">
        <xsd:attribute name="ACTION" type="xsd:string" use="optional"/>
        <xsd:attribute name="Index_n" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_id_key" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_user_cd" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_cd_nm" type="xsd:string" use="optional"/>
        <xsd:attribute name="mat_tp_id" type="xsd:string" use="optional"/>
        <xsd:attribute name="jldw_id" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_use_mark" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_lev_cd" type="xsd:string" use="optional"/>
        <xsd:attribute name="sys_ord_cd" type="xsd:string" use="optional"/>
        <xsd:attribute name="esb_id" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息表格类型 -->
      <xsd:complexType name="MessageTable">
        <xsd:sequence>
          <xsd:element name="ROW" type="tns:BaseDataAuxiliaryItem" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attribute name="TABLENAME" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息数据类型 -->
      <xsd:complexType name="MessageData">
        <xsd:sequence>
          <xsd:element name="TABLE" type="tns:MessageTable" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 消息类型 -->
      <xsd:complexType name="Message">
        <xsd:sequence>
          <xsd:element name="Head" type="tns:MessageHead" minOccurs="0"/>
          <xsd:element name="DATA" type="tns:MessageData" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 字符串数组类型 -->
      <xsd:complexType name="ArrayOfString">
        <xsd:sequence>
          <xsd:element name="string" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 元素定义 -->
      <xsd:element name="SyncBaseDataAuxiliaryRequest" type="tns:Message"/>
      <xsd:element name="SyncBaseDataAuxiliaryResponse" type="tns:Message"/>
      <xsd:element name="QueryBaseDataAuxiliaryRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="materialCode" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="QueryBaseDataAuxiliaryResponse" type="tns:Message"/>
      <xsd:element name="BatchQueryBaseDataAuxiliaryRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="materialCodes" type="tns:ArrayOfString"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="BatchQueryBaseDataAuxiliaryResponse" type="tns:Message"/>

    </xsd:schema>
  </types>

  <!-- 消息定义 -->
  <message name="SyncBaseDataAuxiliarySoapIn">
    <part name="parameters" element="tns:SyncBaseDataAuxiliaryRequest"/>
  </message>
  <message name="SyncBaseDataAuxiliarySoapOut">
    <part name="parameters" element="tns:SyncBaseDataAuxiliaryResponse"/>
  </message>
  <message name="QueryBaseDataAuxiliarySoapIn">
    <part name="parameters" element="tns:QueryBaseDataAuxiliaryRequest"/>
  </message>
  <message name="QueryBaseDataAuxiliarySoapOut">
    <part name="parameters" element="tns:QueryBaseDataAuxiliaryResponse"/>
  </message>
  <message name="BatchQueryBaseDataAuxiliarySoapIn">
    <part name="parameters" element="tns:BatchQueryBaseDataAuxiliaryRequest"/>
  </message>
  <message name="BatchQueryBaseDataAuxiliarySoapOut">
    <part name="parameters" element="tns:BatchQueryBaseDataAuxiliaryResponse"/>
  </message>

  <!-- 端口类型定义 -->
  <portType name="BaseDataAuxiliaryServiceSoap">
    <operation name="SyncBaseDataAuxiliary">
      <documentation>同步辅料基本属性数据</documentation>
      <input message="tns:SyncBaseDataAuxiliarySoapIn"/>
      <output message="tns:SyncBaseDataAuxiliarySoapOut"/>
    </operation>
    <operation name="QueryBaseDataAuxiliary">
      <documentation>查询辅料基本属性</documentation>
      <input message="tns:QueryBaseDataAuxiliarySoapIn"/>
      <output message="tns:QueryBaseDataAuxiliarySoapOut"/>
    </operation>
    <operation name="BatchQueryBaseDataAuxiliary">
      <documentation>批量查询辅料基本属性</documentation>
      <input message="tns:BatchQueryBaseDataAuxiliarySoapIn"/>
      <output message="tns:BatchQueryBaseDataAuxiliarySoapOut"/>
    </operation>
  </portType>

  <!-- 绑定定义 -->
  <binding name="BaseDataAuxiliaryServiceSoap" type="tns:BaseDataAuxiliaryServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="SyncBaseDataAuxiliary">
      <soap:operation soapAction="http://bywms.bj.com/BaseDataAuxiliary/SyncBaseDataAuxiliary" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="QueryBaseDataAuxiliary">
      <soap:operation soapAction="http://bywms.bj.com/BaseDataAuxiliary/QueryBaseDataAuxiliary" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="BatchQueryBaseDataAuxiliary">
      <soap:operation soapAction="http://bywms.bj.com/BaseDataAuxiliary/BatchQueryBaseDataAuxiliary" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>

  <!-- 服务定义 -->
  <service name="BaseDataAuxiliaryService">
    <documentation>辅料基本属性服务</documentation>
    <port name="BaseDataAuxiliaryServiceSoap" binding="tns:BaseDataAuxiliaryServiceSoap">
      <soap:address location="http://localhost:8080/MaterialsWebService/BaseDataAuxiliaryService.asmx"/>
    </port>
  </service>

</definitions>
