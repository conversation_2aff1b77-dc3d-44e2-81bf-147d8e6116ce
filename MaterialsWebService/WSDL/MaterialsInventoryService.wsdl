<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://bywms.bj.com/MaterialsInventory"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             targetNamespace="http://bywms.bj.com/MaterialsInventory"
             elementFormDefault="qualified">

  <!-- 类型定义 -->
  <types>
    <xsd:schema targetNamespace="http://bywms.bj.com/MaterialsInventory" elementFormDefault="qualified">
      
      <!-- 消息头类型 -->
      <xsd:complexType name="MessageHead">
        <xsd:sequence>
          <xsd:element name="Id" type="xsd:string" minOccurs="0"/>
          <xsd:element name="n" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Source" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Target" type="xsd:string" minOccurs="0"/>
          <xsd:element name="SerName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="MsgType" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtCode" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtDesc" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup1" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup2" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup3" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup4" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup5" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Date" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Attrs" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 辅料库存项类型 -->
      <xsd:complexType name="MaterialsInventoryItem">
        <xsd:attribute name="ACTION" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_date" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_brandcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_brandname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_suppliercode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_suppliername" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matunitcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matunitname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_num" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息表格类型 -->
      <xsd:complexType name="MessageTable">
        <xsd:sequence>
          <xsd:element name="ROW" type="tns:MaterialsInventoryItem" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attribute name="TABLENAME" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_date" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_brandcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_brandname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_suppliercode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_suppliername" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matunitcode" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_matunitname" type="xsd:string" use="optional"/>
        <xsd:attribute name="f_num" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息数据类型 -->
      <xsd:complexType name="MessageData">
        <xsd:sequence>
          <xsd:element name="TABLE" type="tns:MessageTable" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 消息类型 -->
      <xsd:complexType name="Message">
        <xsd:sequence>
          <xsd:element name="Head" type="tns:MessageHead" minOccurs="0"/>
          <xsd:element name="DATA" type="tns:MessageData" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 元素定义 -->
      <xsd:element name="GetMaterialsInventoryBillRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="date" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetMaterialsInventoryBillResponse" type="tns:Message"/>
      <xsd:element name="GetMaterialsInventoryByBrandRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="date" type="xsd:string"/>
            <xsd:element name="brandCode" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetMaterialsInventoryByBrandResponse" type="tns:Message"/>
      <xsd:element name="GetMaterialsInventoryByMaterialRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="date" type="xsd:string"/>
            <xsd:element name="materialCode" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetMaterialsInventoryByMaterialResponse" type="tns:Message"/>
      <xsd:element name="GetRealTimeInventoryRequest">
        <xsd:complexType>
          <xsd:sequence/>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetRealTimeInventoryResponse" type="tns:Message"/>

    </xsd:schema>
  </types>

  <!-- 消息定义 -->
  <message name="GetMaterialsInventoryBillSoapIn">
    <part name="parameters" element="tns:GetMaterialsInventoryBillRequest"/>
  </message>
  <message name="GetMaterialsInventoryBillSoapOut">
    <part name="parameters" element="tns:GetMaterialsInventoryBillResponse"/>
  </message>
  <message name="GetMaterialsInventoryByBrandSoapIn">
    <part name="parameters" element="tns:GetMaterialsInventoryByBrandRequest"/>
  </message>
  <message name="GetMaterialsInventoryByBrandSoapOut">
    <part name="parameters" element="tns:GetMaterialsInventoryByBrandResponse"/>
  </message>
  <message name="GetMaterialsInventoryByMaterialSoapIn">
    <part name="parameters" element="tns:GetMaterialsInventoryByMaterialRequest"/>
  </message>
  <message name="GetMaterialsInventoryByMaterialSoapOut">
    <part name="parameters" element="tns:GetMaterialsInventoryByMaterialResponse"/>
  </message>
  <message name="GetRealTimeInventorySoapIn">
    <part name="parameters" element="tns:GetRealTimeInventoryRequest"/>
  </message>
  <message name="GetRealTimeInventorySoapOut">
    <part name="parameters" element="tns:GetRealTimeInventoryResponse"/>
  </message>

  <!-- 端口类型定义 -->
  <portType name="MaterialsInventoryServiceSoap">
    <operation name="GetMaterialsInventoryBill">
      <documentation>获取辅料库存信息</documentation>
      <input message="tns:GetMaterialsInventoryBillSoapIn"/>
      <output message="tns:GetMaterialsInventoryBillSoapOut"/>
    </operation>
    <operation name="GetMaterialsInventoryByBrand">
      <documentation>按品牌查询辅料库存</documentation>
      <input message="tns:GetMaterialsInventoryByBrandSoapIn"/>
      <output message="tns:GetMaterialsInventoryByBrandSoapOut"/>
    </operation>
    <operation name="GetMaterialsInventoryByMaterial">
      <documentation>按物料编码查询库存</documentation>
      <input message="tns:GetMaterialsInventoryByMaterialSoapIn"/>
      <output message="tns:GetMaterialsInventoryByMaterialSoapOut"/>
    </operation>
    <operation name="GetRealTimeInventory">
      <documentation>实时库存查询</documentation>
      <input message="tns:GetRealTimeInventorySoapIn"/>
      <output message="tns:GetRealTimeInventorySoapOut"/>
    </operation>
  </portType>

  <!-- 绑定定义 -->
  <binding name="MaterialsInventoryServiceSoap" type="tns:MaterialsInventoryServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="GetMaterialsInventoryBill">
      <soap:operation soapAction="http://bywms.bj.com/MaterialsInventory/GetMaterialsInventoryBill" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="GetMaterialsInventoryByBrand">
      <soap:operation soapAction="http://bywms.bj.com/MaterialsInventory/GetMaterialsInventoryByBrand" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="GetMaterialsInventoryByMaterial">
      <soap:operation soapAction="http://bywms.bj.com/MaterialsInventory/GetMaterialsInventoryByMaterial" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="GetRealTimeInventory">
      <soap:operation soapAction="http://bywms.bj.com/MaterialsInventory/GetRealTimeInventory" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>

  <!-- 服务定义 -->
  <service name="MaterialsInventoryService">
    <documentation>辅料高架库库存服务</documentation>
    <port name="MaterialsInventoryServiceSoap" binding="tns:MaterialsInventoryServiceSoap">
      <soap:address location="http://localhost:8080/MaterialsWebService/MaterialsInventoryService.asmx"/>
    </port>
  </service>

</definitions>
