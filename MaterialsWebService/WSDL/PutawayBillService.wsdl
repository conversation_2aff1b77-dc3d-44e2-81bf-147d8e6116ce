<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://bywms.bj.com/PutawayBill_FL"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             targetNamespace="http://bywms.bj.com/PutawayBill_FL"
             elementFormDefault="qualified">

  <!-- 类型定义 -->
  <types>
    <xsd:schema targetNamespace="http://bywms.bj.com/PutawayBill_FL" elementFormDefault="qualified">
      
      <!-- 消息头类型 -->
      <xsd:complexType name="MessageHead">
        <xsd:sequence>
          <xsd:element name="Id" type="xsd:string" minOccurs="0"/>
          <xsd:element name="n" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Source" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Target" type="xsd:string" minOccurs="0"/>
          <xsd:element name="SerName" type="xsd:string" minOccurs="0"/>
          <xsd:element name="MsgType" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtCode" type="xsd:string" minOccurs="0"/>
          <xsd:element name="RtDesc" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup1" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup2" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup3" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup4" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Backup5" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Date" type="xsd:string" minOccurs="0"/>
          <xsd:element name="Attrs" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 出库单据明细项类型 -->
      <xsd:complexType name="PutawayBillItem">
        <xsd:attribute name="PK_DJHM" type="xsd:string" use="optional"/>
        <xsd:attribute name="PK_XH" type="xsd:string" use="optional"/>
        <xsd:attribute name="PK_PCXH" type="xsd:string" use="optional"/>
        <xsd:attribute name="MATNR" type="xsd:string" use="optional"/>
        <xsd:attribute name="MAKTX" type="xsd:string" use="optional"/>
        <xsd:attribute name="ZKPCSL" type="xsd:string" use="optional"/>
        <xsd:attribute name="MEINS" type="xsd:string" use="optional"/>
        <xsd:attribute name="PCSL" type="xsd:string" use="optional"/>
        <xsd:attribute name="ZZ_MEINS" type="xsd:string" use="optional"/>
        <xsd:attribute name="LIFNR" type="xsd:string" use="optional"/>
        <xsd:attribute name="ZCKW" type="xsd:string" use="optional"/>
        <xsd:attribute name="ZRKW" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 出库单据明细表类型 -->
      <xsd:complexType name="PutawayBillItemTable">
        <xsd:sequence>
          <xsd:element name="ROW" type="tns:PutawayBillItem" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attribute name="TABLENAME" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 出库单据头类型 -->
      <xsd:complexType name="PutawayBillHeader">
        <xsd:sequence>
          <xsd:element name="TABLE" type="tns:PutawayBillItemTable" minOccurs="0"/>
        </xsd:sequence>
        <xsd:attribute name="ACTION" type="xsd:string" use="optional"/>
        <xsd:attribute name="PK_DJHM" type="xsd:string" use="optional"/>
        <xsd:attribute name="DJLX" type="xsd:string" use="optional"/>
        <xsd:attribute name="WERKS" type="xsd:string" use="optional"/>
        <xsd:attribute name="JHZKRQ" type="xsd:string" use="optional"/>
        <xsd:attribute name="ERDAT" type="xsd:string" use="optional"/>
        <xsd:attribute name="ERNAM" type="xsd:string" use="optional"/>
        <xsd:attribute name="BZ" type="xsd:string" use="optional"/>
        <xsd:attribute name="DEL" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息表格类型 -->
      <xsd:complexType name="MessageTable">
        <xsd:sequence>
          <xsd:element name="ROW" type="tns:PutawayBillHeader" minOccurs="0" maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attribute name="TABLENAME" type="xsd:string" use="optional"/>
      </xsd:complexType>

      <!-- 消息数据类型 -->
      <xsd:complexType name="MessageData">
        <xsd:sequence>
          <xsd:element name="TABLE" type="tns:MessageTable" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 消息类型 -->
      <xsd:complexType name="Message">
        <xsd:sequence>
          <xsd:element name="Head" type="tns:MessageHead" minOccurs="0"/>
          <xsd:element name="DATA" type="tns:MessageData" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>

      <!-- 元素定义 -->
      <xsd:element name="ProcessPutawayBillRequest" type="tns:Message"/>
      <xsd:element name="ProcessPutawayBillResponse" type="tns:Message"/>
      <xsd:element name="QueryPutawayBillStatusRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="documentNumber" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="QueryPutawayBillStatusResponse" type="tns:Message"/>
      <xsd:element name="CancelPutawayBillRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="documentNumber" type="xsd:string"/>
            <xsd:element name="reason" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="CancelPutawayBillResponse" type="tns:MessageHead"/>

    </xsd:schema>
  </types>

  <!-- 消息定义 -->
  <message name="ProcessPutawayBillSoapIn">
    <part name="parameters" element="tns:ProcessPutawayBillRequest"/>
  </message>
  <message name="ProcessPutawayBillSoapOut">
    <part name="parameters" element="tns:ProcessPutawayBillResponse"/>
  </message>
  <message name="QueryPutawayBillStatusSoapIn">
    <part name="parameters" element="tns:QueryPutawayBillStatusRequest"/>
  </message>
  <message name="QueryPutawayBillStatusSoapOut">
    <part name="parameters" element="tns:QueryPutawayBillStatusResponse"/>
  </message>
  <message name="CancelPutawayBillSoapIn">
    <part name="parameters" element="tns:CancelPutawayBillRequest"/>
  </message>
  <message name="CancelPutawayBillSoapOut">
    <part name="parameters" element="tns:CancelPutawayBillResponse"/>
  </message>

  <!-- 端口类型定义 -->
  <portType name="PutawayBillServiceSoap">
    <operation name="ProcessPutawayBill">
      <documentation>处理辅料出库单据</documentation>
      <input message="tns:ProcessPutawayBillSoapIn"/>
      <output message="tns:ProcessPutawayBillSoapOut"/>
    </operation>
    <operation name="QueryPutawayBillStatus">
      <documentation>查询出库单据状态</documentation>
      <input message="tns:QueryPutawayBillStatusSoapIn"/>
      <output message="tns:QueryPutawayBillStatusSoapOut"/>
    </operation>
    <operation name="CancelPutawayBill">
      <documentation>取消出库单据</documentation>
      <input message="tns:CancelPutawayBillSoapIn"/>
      <output message="tns:CancelPutawayBillSoapOut"/>
    </operation>
  </portType>

  <!-- 绑定定义 -->
  <binding name="PutawayBillServiceSoap" type="tns:PutawayBillServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="ProcessPutawayBill">
      <soap:operation soapAction="http://bywms.bj.com/PutawayBill_FL/ProcessPutawayBill" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="QueryPutawayBillStatus">
      <soap:operation soapAction="http://bywms.bj.com/PutawayBill_FL/QueryPutawayBillStatus" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="CancelPutawayBill">
      <soap:operation soapAction="http://bywms.bj.com/PutawayBill_FL/CancelPutawayBill" style="document"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>

  <!-- 服务定义 -->
  <service name="PutawayBillService">
    <documentation>辅料高架库出库服务</documentation>
    <port name="PutawayBillServiceSoap" binding="tns:PutawayBillServiceSoap">
      <soap:address location="http://localhost:8080/MaterialsWebService/PutawayBillService.asmx"/>
    </port>
  </service>

</definitions>
