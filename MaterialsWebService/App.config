<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>

  <system.serviceModel>
    <services>
      <!-- 辅料出库服务配置 -->
      <service name="MaterialsWebService.Services.PutawayBillService" 
               behaviorConfiguration="ServiceBehavior">
        <endpoint address="" 
                  binding="basicHttpBinding" 
                  bindingConfiguration="BasicHttpBinding_Config"
                  contract="MaterialsWebService.Services.IPutawayBillService" />
        <endpoint address="mex" 
                  binding="mexHttpBinding" 
                  contract="IMetadataExchange" />
        <host>
          <baseAddresses>
            <add baseAddress="http://localhost:8080/MaterialsWebService/PutawayBillService" />
          </baseAddresses>
        </host>
      </service>

      <!-- 辅料基本属性服务配置 -->
      <service name="MaterialsWebService.Services.BaseDataAuxiliaryService" 
               behaviorConfiguration="ServiceBehavior">
        <endpoint address="" 
                  binding="basicHttpBinding" 
                  bindingConfiguration="BasicHttpBinding_Config"
                  contract="MaterialsWebService.Services.IBaseDataAuxiliaryService" />
        <endpoint address="mex" 
                  binding="mexHttpBinding" 
                  contract="IMetadataExchange" />
        <host>
          <baseAddresses>
            <add baseAddress="http://localhost:8080/MaterialsWebService/BaseDataAuxiliaryService" />
          </baseAddresses>
        </host>
      </service>

      <!-- 辅料库存服务配置 -->
      <service name="MaterialsWebService.Services.MaterialsInventoryService" 
               behaviorConfiguration="ServiceBehavior">
        <endpoint address="" 
                  binding="basicHttpBinding" 
                  bindingConfiguration="BasicHttpBinding_Config"
                  contract="MaterialsWebService.Services.IMaterialsInventoryService" />
        <endpoint address="mex" 
                  binding="mexHttpBinding" 
                  contract="IMetadataExchange" />
        <host>
          <baseAddresses>
            <add baseAddress="http://localhost:8080/MaterialsWebService/MaterialsInventoryService" />
          </baseAddresses>
        </host>
      </service>
    </services>

    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_Config" 
                 maxBufferSize="**********" 
                 maxReceivedMessageSize="**********"
                 messageEncoding="Text"
                 textEncoding="utf-8">
          <readerQuotas maxDepth="32" 
                        maxStringContentLength="**********" 
                        maxArrayLength="**********" 
                        maxBytesPerRead="**********" 
                        maxNameTableCharCount="**********" />
          <security mode="None">
            <transport clientCredentialType="None" />
          </security>
        </binding>
      </basicHttpBinding>
    </bindings>

    <behaviors>
      <serviceBehaviors>
        <behavior name="ServiceBehavior">
          <serviceMetadata httpGetEnabled="true" httpsGetEnabled="false" />
          <serviceDebug includeExceptionDetailInFaults="true" />
          <dataContractSerializer maxItemsInObjectGraph="**********" />
        </behavior>
      </serviceBehaviors>
    </behaviors>

    <serviceHostingEnvironment aspNetCompatibilityEnabled="true" multipleSiteBindingsEnabled="true" />
  </system.serviceModel>

  <appSettings>
    <!-- 数据库连接字符串 -->
    <add key="ConnectionString" value="Data Source=localhost;Initial Catalog=MaterialsDB;Integrated Security=True" />
    
    <!-- 日志配置 -->
    <add key="LogLevel" value="Info" />
    <add key="LogPath" value="C:\Logs\MaterialsWebService\" />
    
    <!-- 业务配置 -->
    <add key="DefaultPlant" value="8100" />
    <add key="DefaultSource" value="BYWMS" />
    <add key="MaxRetryCount" value="3" />
    <add key="TimeoutSeconds" value="30" />
  </appSettings>

  <connectionStrings>
    <add name="MaterialsDB" 
         connectionString="Data Source=localhost;Initial Catalog=MaterialsDB;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>

  <system.web>
    <compilation debug="true" targetFramework="4.7.2" />
    <httpRuntime targetFramework="4.7.2" maxRequestLength="**********" executionTimeout="300" />
    <pages controlRenderingCompatibilityVersion="4.0" />
  </system.web>

</configuration>
