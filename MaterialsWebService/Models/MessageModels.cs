using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace MaterialsWebService.Models
{
    /// <summary>
    /// 消息头信息
    /// </summary>
    [DataContract]
    [XmlRoot("Head")]
    public class MessageHead
    {
        [DataMember]
        [XmlElement("Id")]
        public string Id { get; set; }

        [DataMember]
        [XmlElement("n")]
        public string Name { get; set; }

        [DataMember]
        [XmlElement("Source")]
        public string Source { get; set; }

        [DataMember]
        [XmlElement("Target")]
        public string Target { get; set; }

        [DataMember]
        [XmlElement("SerName")]
        public string ServiceName { get; set; }

        [DataMember]
        [XmlElement("MsgType")]
        public string MessageType { get; set; }

        [DataMember]
        [XmlElement("RtCode")]
        public string ReturnCode { get; set; }

        [DataMember]
        [XmlElement("RtDesc")]
        public string ReturnDescription { get; set; }

        [DataMember]
        [XmlElement("Backup1")]
        public string Backup1 { get; set; }

        [DataMember]
        [XmlElement("Backup2")]
        public string Backup2 { get; set; }

        [DataMember]
        [XmlElement("Backup3")]
        public string Backup3 { get; set; }

        [DataMember]
        [XmlElement("Backup4")]
        public string Backup4 { get; set; }

        [DataMember]
        [XmlElement("Backup5")]
        public string Backup5 { get; set; }

        [DataMember]
        [XmlElement("Date")]
        public string Date { get; set; }

        [DataMember]
        [XmlElement("Attrs")]
        public string Attributes { get; set; }
    }

    /// <summary>
    /// 辅料出库单据信息
    /// </summary>
    [DataContract]
    [XmlRoot("ROW")]
    public class PutawayBillHeader
    {
        [DataMember]
        [XmlAttribute("ACTION")]
        public string Action { get; set; }

        [DataMember]
        [XmlAttribute("PK_DJHM")]
        public string DocumentNumber { get; set; }

        [DataMember]
        [XmlAttribute("DJLX")]
        public string DocumentType { get; set; }

        [DataMember]
        [XmlAttribute("WERKS")]
        public string Plant { get; set; }

        [DataMember]
        [XmlAttribute("JHZKRQ")]
        public string PlannedTransferDate { get; set; }

        [DataMember]
        [XmlAttribute("ERDAT")]
        public string CreateDate { get; set; }

        [DataMember]
        [XmlAttribute("ERNAM")]
        public string Creator { get; set; }

        [DataMember]
        [XmlAttribute("BZ")]
        public string Remark { get; set; }

        [DataMember]
        [XmlAttribute("DEL")]
        public string DeleteFlag { get; set; }

        [DataMember]
        [XmlElement("TABLE")]
        public PutawayBillItemTable ItemTable { get; set; }
    }

    /// <summary>
    /// 辅料出库明细表
    /// </summary>
    [DataContract]
    public class PutawayBillItemTable
    {
        [DataMember]
        [XmlAttribute("TABLENAME")]
        public string TableName { get; set; }

        [DataMember]
        [XmlElement("ROW")]
        public List<PutawayBillItem> Items { get; set; }
    }

    /// <summary>
    /// 辅料出库明细项
    /// </summary>
    [DataContract]
    [XmlRoot("ROW")]
    public class PutawayBillItem
    {
        [DataMember]
        [XmlAttribute("PK_DJHM")]
        public string DocumentNumber { get; set; }

        [DataMember]
        [XmlAttribute("PK_XH")]
        public string LineNumber { get; set; }

        [DataMember]
        [XmlAttribute("PK_PCXH")]
        public string ProductionLineNumber { get; set; }

        [DataMember]
        [XmlAttribute("MATNR")]
        public string MaterialCode { get; set; }

        [DataMember]
        [XmlAttribute("MAKTX")]
        public string MaterialDescription { get; set; }

        [DataMember]
        [XmlAttribute("ZKPCSL")]
        public string TransferProductionQuantity { get; set; }

        [DataMember]
        [XmlAttribute("MEINS")]
        public string Unit { get; set; }

        [DataMember]
        [XmlAttribute("PCSL")]
        public string ProductionQuantity { get; set; }

        [DataMember]
        [XmlAttribute("ZZ_MEINS")]
        public string UnitCode { get; set; }

        [DataMember]
        [XmlAttribute("LIFNR")]
        public string SupplierCode { get; set; }

        [DataMember]
        [XmlAttribute("ZCKW")]
        public string OutboundWarehouse { get; set; }

        [DataMember]
        [XmlAttribute("ZRKW")]
        public string InboundWarehouse { get; set; }
    }

    /// <summary>
    /// 辅料基本属性信息
    /// </summary>
    [DataContract]
    [XmlRoot("ROW")]
    public class BaseDataAuxiliaryItem
    {
        [DataMember]
        [XmlAttribute("ACTION")]
        public string Action { get; set; }

        [DataMember]
        [XmlAttribute("Index_n")]
        public string Index { get; set; }

        [DataMember]
        [XmlAttribute("sys_id_key")]
        public string SystemIdKey { get; set; }

        [DataMember]
        [XmlAttribute("sys_user_cd")]
        public string SystemUserCode { get; set; }

        [DataMember]
        [XmlAttribute("sys_cd_nm")]
        public string SystemCodeName { get; set; }

        [DataMember]
        [XmlAttribute("mat_tp_id")]
        public string MaterialTypeId { get; set; }

        [DataMember]
        [XmlAttribute("jldw_id")]
        public string UnitId { get; set; }

        [DataMember]
        [XmlAttribute("sys_use_mark")]
        public string SystemUseMark { get; set; }

        [DataMember]
        [XmlAttribute("sys_lev_cd")]
        public string SystemLevelCode { get; set; }

        [DataMember]
        [XmlAttribute("sys_ord_cd")]
        public string SystemOrderCode { get; set; }

        [DataMember]
        [XmlAttribute("esb_id")]
        public string EsbId { get; set; }
    }

    /// <summary>
    /// 辅料库存信息
    /// </summary>
    [DataContract]
    [XmlRoot("ROW")]
    public class MaterialsInventoryItem
    {
        [DataMember]
        [XmlAttribute("ACTION")]
        public string Action { get; set; }

        [DataMember]
        [XmlAttribute("f_date")]
        public string Date { get; set; }

        [DataMember]
        [XmlAttribute("f_brandcode")]
        public string BrandCode { get; set; }

        [DataMember]
        [XmlAttribute("f_brandname")]
        public string BrandName { get; set; }

        [DataMember]
        [XmlAttribute("f_matcode")]
        public string MaterialCode { get; set; }

        [DataMember]
        [XmlAttribute("f_matname")]
        public string MaterialName { get; set; }

        [DataMember]
        [XmlAttribute("f_suppliercode")]
        public string SupplierCode { get; set; }

        [DataMember]
        [XmlAttribute("f_suppliername")]
        public string SupplierName { get; set; }

        [DataMember]
        [XmlAttribute("f_matunitcode")]
        public string MaterialUnitCode { get; set; }

        [DataMember]
        [XmlAttribute("f_matunitname")]
        public string MaterialUnitName { get; set; }

        [DataMember]
        [XmlAttribute("f_num")]
        public string Quantity { get; set; }
    }

    /// <summary>
    /// 通用消息结构
    /// </summary>
    [DataContract]
    [XmlRoot("Msg")]
    public class Message<T>
    {
        [DataMember]
        [XmlElement("Head")]
        public MessageHead Head { get; set; }

        [DataMember]
        [XmlElement("DATA")]
        public MessageData<T> Data { get; set; }
    }

    /// <summary>
    /// 消息数据部分
    /// </summary>
    [DataContract]
    public class MessageData<T>
    {
        [DataMember]
        [XmlElement("TABLE")]
        public MessageTable<T> Table { get; set; }
    }

    /// <summary>
    /// 消息表格结构
    /// </summary>
    [DataContract]
    public class MessageTable<T>
    {
        [DataMember]
        [XmlAttribute("TABLENAME")]
        public string TableName { get; set; }

        [DataMember]
        [XmlElement("ROW")]
        public List<T> Rows { get; set; }
    }
}
