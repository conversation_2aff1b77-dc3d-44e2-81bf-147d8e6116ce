# 宝鸡卷烟厂辅料管理Web服务

## 项目概述

本项目是基于 C# .NET Framework 4.7.2 开发的 WSDL Web 服务，用于宝鸡卷烟厂的辅料管理系统。项目包含三个主要接口，用于处理辅料出库、基本属性同步和库存查询等业务功能。

## 项目结构

```
MaterialsWebService/
├── MaterialsWebService.sln          # 解决方案文件
├── MaterialsWebService/
│   ├── MaterialsWebService.csproj   # 项目文件
│   ├── App.config                   # 配置文件
│   ├── Properties/
│   │   └── AssemblyInfo.cs          # 程序集信息
│   ├── Models/
│   │   └── MessageModels.cs         # 数据模型定义
│   ├── Services/                    # 服务接口和实现
│   │   ├── IPutawayBillService.cs           # 辅料出库接口
│   │   ├── PutawayBillService.cs            # 辅料出库服务实现
│   │   ├── IBaseDataAuxiliaryService.cs     # 辅料基本属性接口
│   │   ├── BaseDataAuxiliaryService.cs      # 辅料基本属性服务实现
│   │   ├── IMaterialsInventoryService.cs    # 辅料库存接口
│   │   └── MaterialsInventoryService.cs     # 辅料库存服务实现
│   └── WSDL/                        # WSDL 定义文件
│       ├── PutawayBillService.wsdl          # 辅料出库服务WSDL
│       ├── BaseDataAuxiliaryService.wsdl    # 辅料基本属性服务WSDL
│       └── MaterialsInventoryService.wsdl   # 辅料库存服务WSDL
└── README.md                        # 项目说明文档
```

## 三个主要接口

### 1. 辅料高架库出库接口 (PutawayBill_FL)

**命名空间**: `http://bywms.bj.com/PutawayBill_FL`

**主要功能**:
- `ProcessPutawayBill`: 处理辅料出库单据
- `QueryPutawayBillStatus`: 查询出库单据状态
- `CancelPutawayBill`: 取消出库单据

**服务地址**: `http://localhost:8080/MaterialsWebService/PutawayBillService`

### 2. 辅料基本属性接口 (BaseDataAuxiliary)

**命名空间**: `http://bywms.bj.com/BaseDataAuxiliary`

**主要功能**:
- `SyncBaseDataAuxiliary`: 同步辅料基本属性数据
- `QueryBaseDataAuxiliary`: 查询单个辅料基本属性
- `BatchQueryBaseDataAuxiliary`: 批量查询辅料基本属性

**服务地址**: `http://localhost:8080/MaterialsWebService/BaseDataAuxiliaryService`

### 3. 辅料高架库库存接口 (getMaterialsInventoryBill)

**命名空间**: `http://bywms.bj.com/MaterialsInventory`

**主要功能**:
- `GetMaterialsInventoryBill`: 获取辅料库存信息
- `GetMaterialsInventoryByBrand`: 按品牌查询辅料库存
- `GetMaterialsInventoryByMaterial`: 按物料编码查询库存
- `GetRealTimeInventory`: 实时库存查询

**服务地址**: `http://localhost:8080/MaterialsWebService/MaterialsInventoryService`

## 数据模型

### 消息头 (MessageHead)
包含消息的基本信息，如消息ID、来源、目标、服务名称、返回码等。

### 辅料出库相关模型
- `PutawayBillHeader`: 出库单据头信息
- `PutawayBillItem`: 出库单据明细项
- `PutawayBillItemTable`: 出库单据明细表

### 辅料基本属性模型
- `BaseDataAuxiliaryItem`: 辅料基本属性项

### 辅料库存模型
- `MaterialsInventoryItem`: 辅料库存项

### 通用消息结构
- `Message<T>`: 通用消息结构
- `MessageData<T>`: 消息数据部分
- `MessageTable<T>`: 消息表格结构

## 技术特性

- **框架**: .NET Framework 4.7.2
- **服务类型**: WCF Web Services (SOAP)
- **数据格式**: XML
- **编码**: UTF-8 (支持中文)
- **绑定**: BasicHttpBinding
- **安全**: 无安全认证 (可根据需要配置)

## 配置说明

### 服务配置
在 `App.config` 中配置了三个服务的端点、绑定和行为。

### 数据库配置
```xml
<connectionStrings>
  <add name="MaterialsDB" 
       connectionString="Data Source=localhost;Initial Catalog=MaterialsDB;Integrated Security=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 应用程序设置
```xml
<appSettings>
  <add key="DefaultPlant" value="8100" />
  <add key="DefaultSource" value="BYWMS" />
  <add key="MaxRetryCount" value="3" />
  <add key="TimeoutSeconds" value="30" />
</appSettings>
```

## 部署说明

1. **编译项目**: 使用 Visual Studio 或 MSBuild 编译项目
2. **配置IIS**: 将编译后的文件部署到 IIS 服务器
3. **配置数据库**: 根据需要配置数据库连接字符串
4. **测试服务**: 访问 WSDL 地址验证服务是否正常运行

## WSDL 访问地址

- 辅料出库服务: `http://localhost:8080/MaterialsWebService/PutawayBillService?wsdl`
- 辅料基本属性服务: `http://localhost:8080/MaterialsWebService/BaseDataAuxiliaryService?wsdl`
- 辅料库存服务: `http://localhost:8080/MaterialsWebService/MaterialsInventoryService?wsdl`

## 示例调用

### 辅料出库单据处理示例

```xml
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ProcessPutawayBillRequest xmlns="http://bywms.bj.com/PutawayBill_FL">
      <Head>
        <Id>000000000002398637</Id>
        <n>辅料高架库出库接口</n>
        <Source>ZYERP</Source>
        <Target>BYWMS</Target>
        <SerName>PutawayBill_FL</SerName>
        <MsgType>0</MsgType>
      </Head>
      <DATA>
        <TABLE TABLENAME="FLGJK">
          <ROW ACTION="" PK_DJHM="P20000211518" DJLX="P2" WERKS="8100">
            <!-- 单据明细 -->
          </ROW>
        </TABLE>
      </DATA>
    </ProcessPutawayBillRequest>
  </soap:Body>
</soap:Envelope>
```

## 注意事项

1. 本项目为示例实现，实际使用时需要根据具体业务需求进行调整
2. 数据库操作部分需要根据实际数据库结构实现
3. 错误处理和日志记录需要根据生产环境要求完善
4. 安全认证机制需要根据实际需求配置

## 开发环境要求

- Visual Studio 2017 或更高版本
- .NET Framework 4.7.2 或更高版本
- IIS 7.0 或更高版本 (用于部署)
- SQL Server (如果使用数据库)

## 联系信息

如有问题或建议，请联系开发团队。
