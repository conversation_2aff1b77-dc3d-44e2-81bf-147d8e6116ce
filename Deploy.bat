@echo off
echo ========================================
echo 宝鸡卷烟厂辅料管理Web服务部署脚本
echo ========================================

REM 设置变量
set PROJECT_NAME=MaterialsWebService
set BUILD_CONFIG=Release
set IIS_SITE_NAME=MaterialsWebService
set IIS_APP_POOL=MaterialsWebServicePool
set DEPLOY_PATH=C:\inetpub\wwwroot\%PROJECT_NAME%

echo.
echo 1. 清理并编译项目...
echo -------------------
if exist "bin\%BUILD_CONFIG%" (
    rmdir /s /q "bin\%BUILD_CONFIG%"
)

REM 使用MSBuild编译项目
"%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" %PROJECT_NAME%.sln /p:Configuration=%BUILD_CONFIG% /p:Platform="Any CPU"

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！

echo.
echo 2. 创建IIS应用程序池...
echo ----------------------
REM 删除现有应用程序池（如果存在）
%windir%\system32\inetsrv\appcmd.exe delete apppool "%IIS_APP_POOL%" 2>nul

REM 创建新的应用程序池
%windir%\system32\inetsrv\appcmd.exe add apppool /name:"%IIS_APP_POOL%" /managedRuntimeVersion:"v4.0" /processModel.identityType:"ApplicationPoolIdentity"

if %ERRORLEVEL% neq 0 (
    echo 创建应用程序池失败！
    pause
    exit /b 1
)

echo 应用程序池创建成功！

echo.
echo 3. 创建部署目录...
echo ----------------
if exist "%DEPLOY_PATH%" (
    rmdir /s /q "%DEPLOY_PATH%"
)
mkdir "%DEPLOY_PATH%"

echo.
echo 4. 复制文件到部署目录...
echo ----------------------
xcopy /s /e /y "%PROJECT_NAME%\bin\%BUILD_CONFIG%\*" "%DEPLOY_PATH%\bin\"
xcopy /s /e /y "%PROJECT_NAME%\WSDL\*" "%DEPLOY_PATH%\WSDL\"
copy "%PROJECT_NAME%\App.config" "%DEPLOY_PATH%\web.config"

REM 创建服务文件
echo ^<?xml version="1.0" encoding="utf-8"?^> > "%DEPLOY_PATH%\PutawayBillService.asmx"
echo ^<%@ WebService Language="C#" CodeBehind="~/bin/MaterialsWebService.dll" Class="MaterialsWebService.Services.PutawayBillService" %^> >> "%DEPLOY_PATH%\PutawayBillService.asmx"

echo ^<?xml version="1.0" encoding="utf-8"?^> > "%DEPLOY_PATH%\BaseDataAuxiliaryService.asmx"
echo ^<%@ WebService Language="C#" CodeBehind="~/bin/MaterialsWebService.dll" Class="MaterialsWebService.Services.BaseDataAuxiliaryService" %^> >> "%DEPLOY_PATH%\BaseDataAuxiliaryService.asmx"

echo ^<?xml version="1.0" encoding="utf-8"?^> > "%DEPLOY_PATH%\MaterialsInventoryService.asmx"
echo ^<%@ WebService Language="C#" CodeBehind="~/bin/MaterialsWebService.dll" Class="MaterialsWebService.Services.MaterialsInventoryService" %^> >> "%DEPLOY_PATH%\MaterialsInventoryService.asmx"

echo 文件复制完成！

echo.
echo 5. 创建IIS网站...
echo ---------------
REM 删除现有网站（如果存在）
%windir%\system32\inetsrv\appcmd.exe delete site "%IIS_SITE_NAME%" 2>nul

REM 创建新网站
%windir%\system32\inetsrv\appcmd.exe add site /name:"%IIS_SITE_NAME%" /physicalPath:"%DEPLOY_PATH%" /bindings:"http/*:8080:"

if %ERRORLEVEL% neq 0 (
    echo 创建网站失败！
    pause
    exit /b 1
)

REM 设置网站使用指定的应用程序池
%windir%\system32\inetsrv\appcmd.exe set app "%IIS_SITE_NAME%/" /applicationPool:"%IIS_APP_POOL%"

echo 网站创建成功！

echo.
echo 6. 启动服务...
echo ------------
%windir%\system32\inetsrv\appcmd.exe start apppool "%IIS_APP_POOL%"
%windir%\system32\inetsrv\appcmd.exe start site "%IIS_SITE_NAME%"

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 服务访问地址：
echo - 辅料出库服务: http://localhost:8080/PutawayBillService.asmx
echo - 辅料基本属性服务: http://localhost:8080/BaseDataAuxiliaryService.asmx
echo - 辅料库存服务: http://localhost:8080/MaterialsInventoryService.asmx
echo.
echo WSDL访问地址：
echo - 辅料出库服务: http://localhost:8080/PutawayBillService.asmx?wsdl
echo - 辅料基本属性服务: http://localhost:8080/BaseDataAuxiliaryService.asmx?wsdl
echo - 辅料库存服务: http://localhost:8080/MaterialsInventoryService.asmx?wsdl
echo.

pause
