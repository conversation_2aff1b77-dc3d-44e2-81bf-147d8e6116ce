using System;
using System.ServiceModel;
using System.Collections.Generic;

namespace TestClient
{
    /// <summary>
    /// 测试客户端程序
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("宝鸡卷烟厂辅料管理Web服务测试客户端");
            Console.WriteLine("========================================");

            try
            {
                // 测试辅料出库服务
                TestPutawayBillService();

                // 测试辅料基本属性服务
                TestBaseDataAuxiliaryService();

                // 测试辅料库存服务
                TestMaterialsInventoryService();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试辅料出库服务
        /// </summary>
        static void TestPutawayBillService()
        {
            Console.WriteLine("\n1. 测试辅料出库服务");
            Console.WriteLine("-------------------");

            try
            {
                var binding = new BasicHttpBinding();
                var endpoint = new EndpointAddress("http://localhost:8080/MaterialsWebService/PutawayBillService");
                
                using (var factory = new ChannelFactory<IPutawayBillServiceClient>(binding, endpoint))
                {
                    var client = factory.CreateChannel();

                    // 测试查询单据状态
                    Console.WriteLine("测试查询单据状态...");
                    var result = client.QueryPutawayBillStatus("P20000211518");
                    
                    if (result?.Head?.ReturnCode == "1")
                    {
                        Console.WriteLine("✓ 查询单据状态成功");
                    }
                    else
                    {
                        Console.WriteLine("✗ 查询单据状态失败");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 辅料出库服务测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试辅料基本属性服务
        /// </summary>
        static void TestBaseDataAuxiliaryService()
        {
            Console.WriteLine("\n2. 测试辅料基本属性服务");
            Console.WriteLine("----------------------");

            try
            {
                var binding = new BasicHttpBinding();
                var endpoint = new EndpointAddress("http://localhost:8080/MaterialsWebService/BaseDataAuxiliaryService");
                
                using (var factory = new ChannelFactory<IBaseDataAuxiliaryServiceClient>(binding, endpoint))
                {
                    var client = factory.CreateChannel();

                    // 测试查询基本属性
                    Console.WriteLine("测试查询基本属性...");
                    var result = client.QueryBaseDataAuxiliary("8734945FL");
                    
                    if (result?.Head?.ReturnCode == "1")
                    {
                        Console.WriteLine("✓ 查询基本属性成功");
                    }
                    else
                    {
                        Console.WriteLine("✗ 查询基本属性失败");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 辅料基本属性服务测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试辅料库存服务
        /// </summary>
        static void TestMaterialsInventoryService()
        {
            Console.WriteLine("\n3. 测试辅料库存服务");
            Console.WriteLine("------------------");

            try
            {
                var binding = new BasicHttpBinding();
                var endpoint = new EndpointAddress("http://localhost:8080/MaterialsWebService/MaterialsInventoryService");
                
                using (var factory = new ChannelFactory<IMaterialsInventoryServiceClient>(binding, endpoint))
                {
                    var client = factory.CreateChannel();

                    // 测试获取库存信息
                    Console.WriteLine("测试获取库存信息...");
                    var result = client.GetMaterialsInventoryBill("20250508");
                    
                    if (result?.Head?.ReturnCode == "1")
                    {
                        Console.WriteLine("✓ 获取库存信息成功");
                        if (result.Data?.Table?.Rows != null)
                        {
                            Console.WriteLine($"  返回 {result.Data.Table.Rows.Count} 条库存记录");
                        }
                    }
                    else
                    {
                        Console.WriteLine("✗ 获取库存信息失败");
                    }

                    // 测试实时库存查询
                    Console.WriteLine("测试实时库存查询...");
                    var realtimeResult = client.GetRealTimeInventory();
                    
                    if (realtimeResult?.Head?.ReturnCode == "1")
                    {
                        Console.WriteLine("✓ 实时库存查询成功");
                    }
                    else
                    {
                        Console.WriteLine("✗ 实时库存查询失败");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 辅料库存服务测试失败: {ex.Message}");
            }
        }
    }

    // 简化的服务接口定义（实际使用时应该通过添加服务引用自动生成）
    [ServiceContract]
    public interface IPutawayBillServiceClient
    {
        [OperationContract]
        MessageResponse QueryPutawayBillStatus(string documentNumber);
    }

    [ServiceContract]
    public interface IBaseDataAuxiliaryServiceClient
    {
        [OperationContract]
        MessageResponse QueryBaseDataAuxiliary(string materialCode);
    }

    [ServiceContract]
    public interface IMaterialsInventoryServiceClient
    {
        [OperationContract]
        MessageResponse GetMaterialsInventoryBill(string date);

        [OperationContract]
        MessageResponse GetRealTimeInventory();
    }

    // 简化的响应类型
    public class MessageResponse
    {
        public MessageHead Head { get; set; }
        public MessageData Data { get; set; }
    }

    public class MessageHead
    {
        public string ReturnCode { get; set; }
        public string ReturnDescription { get; set; }
    }

    public class MessageData
    {
        public MessageTable Table { get; set; }
    }

    public class MessageTable
    {
        public List<object> Rows { get; set; }
    }
}
